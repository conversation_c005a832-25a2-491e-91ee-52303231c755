/* Custom CSS for Treasure Hunt Adventures */

/* Root Variables */
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #f8c146;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --beige-bg: #F5F5DC;
    --text-muted: #6c757d;
    --border-radius: 0.5rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Global Styles */
body {
    background-color: var(--beige-bg);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Navigation */
.navbar {
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--secondary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--beige-bg) 0%, #f0f0e6 100%);
    padding-top: 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-image img {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    transition: transform 0.3s ease;
}

.hero-image img:hover {
    transform: scale(1.02);
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #3d6bb3);
    box-shadow: var(--box-shadow);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #1e4080, var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Plan Cards */
.plan-card {
    transition: transform 0.3s ease;
}

.plan-card:hover {
    transform: translateY(-5px);
}

.plan-item {
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.plan-item:hover {
    box-shadow: var(--box-shadow-lg);
}

.plan-item .card-header {
    background: linear-gradient(45deg, var(--primary-color), #3d6bb3) !important;
    border: none;
    padding: 1.25rem;
    position: relative;
}

.difficulty-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.difficulty-badge.beginner {
    background-color: var(--success-color);
}

.difficulty-badge.intermediate {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.difficulty-badge.advanced {
    background-color: var(--danger-color);
}

.plan-details {
    background-color: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin: 1rem 0;
}

/* Filter Buttons */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
}

/* Sections */
section {
    padding: 4rem 0;
}

.bg-light {
    background-color: #f8f9fa !important;
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* File Upload */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(44, 90, 160, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(44, 90, 160, 0.1);
}

.file-info {
    background-color: var(--light-color);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    margin-top: 0.5rem;
}

/* Progress Steps */
.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.progress-step {
    flex: 1;
    text-align: center;
    position: relative;
}

.progress-step::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #dee2e6;
    z-index: 1;
}

.progress-step:first-child::before {
    left: 50%;
}

.progress-step:last-child::before {
    right: 50%;
}

.progress-step.active::before {
    background-color: var(--primary-color);
}

.step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #dee2e6;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    position: relative;
    z-index: 2;
    font-weight: 600;
}

.progress-step.active .step-circle {
    background-color: var(--primary-color);
}

.progress-step.completed .step-circle {
    background-color: var(--success-color);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.25rem;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Footer */
footer {
    margin-top: auto;
}

.social-links a {
    font-size: 1.25rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: var(--secondary-color) !important;
}

.footer-links a {
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding-top: 100px;
        text-align: center;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .display-5 {
        font-size: 1.75rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.25rem;
        font-size: 1rem;
    }
    
    .plan-details {
        margin: 0.5rem 0;
        padding: 0.75rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .btn-group {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .btn-group .btn {
        border-radius: var(--border-radius) !important;
        flex: 1;
        min-width: calc(50% - 0.125rem);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.shadow-custom {
    box-shadow: var(--box-shadow-lg);
}

.rounded-custom {
    border-radius: var(--border-radius);
}
