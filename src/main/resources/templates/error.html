<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error - Treasure Hunt Adventures</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
            --beige-bg: #F5F5DC;
        }
        
        body {
            background: linear-gradient(135deg, var(--beige-bg) 0%, #f0f0e6 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .error-container {
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .error-icon {
            font-size: 6rem;
            color: var(--primary-color);
            margin-bottom: 2rem;
            animation: bounce 2s infinite;
        }
        
        .error-code {
            font-size: 4rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .error-message {
            font-size: 1.5rem;
            color: #6c757d;
            margin-bottom: 2rem;
        }
        
        .error-description {
            color: #6c757d;
            margin-bottom: 3rem;
            line-height: 1.6;
        }
        
        .btn-home {
            background: linear-gradient(45deg, var(--primary-color), #3d6bb3);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-home:hover {
            background: linear-gradient(45deg, #1e4080, var(--primary-color));
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(44, 90, 160, 0.3);
            color: white;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }
        
        .error-details {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            text-align: left;
        }
        
        .error-details h5 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .error-details p {
            margin-bottom: 0.5rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-container">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <div class="error-code" th:text="${status ?: '500'}">500</div>
            
            <h1 class="error-message">Oops! Something went wrong</h1>
            
            <p class="error-description">
                We're sorry, but it looks like our treasure map got a bit scrambled! 
                Don't worry, our team of expert adventurers is working to fix this issue.
            </p>
            
            <div class="mb-4">
                <a href="/" class="btn-home">
                    <i class="fas fa-home me-2"></i>
                    Return to Home
                </a>
            </div>
            
            <!-- Error Details (only show in development) -->
            <div th:if="${error}" class="error-details">
                <h5><i class="fas fa-bug me-2"></i>Technical Details</h5>
                <p><strong>Error:</strong> <span th:text="${error}">Error message</span></p>
                <p th:if="${message}"><strong>Message:</strong> <span th:text="${message}">Error details</span></p>
                <p th:if="${path}"><strong>Path:</strong> <span th:text="${path}">Request path</span></p>
                <p><strong>Timestamp:</strong> <span th:text="${timestamp}">Timestamp</span></p>
            </div>
            
            <div class="mt-4">
                <p class="text-muted">
                    <small>
                        If this problem persists, please contact our support team at 
                        <a href="mailto:<EMAIL>" class="text-decoration-none">
                            <EMAIL>
                        </a>
                    </small>
                </p>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
