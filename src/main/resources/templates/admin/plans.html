<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Plans - Treasure Hunt Adventures Admin</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
            --sidebar-bg: #343a40;
            --sidebar-width: 250px;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            padding-top: 1rem;
            z-index: 1000;
            overflow-y: auto;
        }
        
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: var(--primary-color);
        }
        
        .main-content {
            margin-left: var(--sidebar-width);
            padding: 2rem;
        }
        
        .plan-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s ease;
        }
        
        .plan-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="px-3 mb-4">
            <h4 class="text-white">
                <i class="fas fa-map-marked-alt me-2"></i>
                Admin Panel
            </h4>
        </div>
        
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="/admin">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="/admin/plans">
                    <i class="fas fa-map me-2"></i>
                    Manage Plans
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/registrations">
                    <i class="fas fa-users me-2"></i>
                    Registrations
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/" target="_blank">
                    <i class="fas fa-external-link-alt me-2"></i>
                    View Website
                </a>
            </li>
            <li class="nav-item mt-4">
                <a class="nav-link text-danger" href="/admin/logout">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    Logout
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">Manage Plans</h1>
                <p class="text-muted">Create and manage treasure hunt plans</p>
            </div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPlanModal">
                <i class="fas fa-plus me-2"></i>
                Create New Plan
            </button>
        </div>

        <!-- Alert Messages -->
        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span th:text="${error}">Error message</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <span th:text="${success}">Success message</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- Plans Grid -->
        <div class="row g-4">
            <div th:if="${plans.empty}" class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-map fs-1 text-muted mb-3"></i>
                    <h4 class="text-muted">No plans found</h4>
                    <p class="text-muted">Create your first treasure hunt plan to get started.</p>
                </div>
            </div>
            
            <div th:each="plan : ${plans}" class="col-lg-4 col-md-6">
                <div class="card plan-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0" th:text="${plan.name}">Plan Name</h5>
                        <span class="badge" 
                              th:classappend="${plan.status.name() == 'ACTIVE' ? 'bg-success' : 'bg-secondary'}"
                              th:text="${plan.status}">Status</span>
                    </div>
                    <div class="card-body">
                        <p class="card-text text-muted" th:text="${plan.getTruncatedDescription(100)}">Description...</p>
                        
                        <div class="mb-3">
                            <small class="text-muted d-block">
                                <i class="fas fa-signal me-1"></i>
                                <span th:text="${plan.difficultyLevel}">Difficulty</span>
                            </small>
                            <small class="text-muted d-block">
                                <i class="fas fa-clock me-1"></i>
                                <span th:text="${plan.durationHours} + ' hours'">Duration</span>
                            </small>
                            <small class="text-muted d-block">
                                <i class="fas fa-users me-1"></i>
                                <span th:text="${plan.maxParticipants} + ' max participants'">Max participants</span>
                            </small>
                            <small class="text-muted d-block">
                                <i class="fas fa-dollar-sign me-1"></i>
                                $<span th:text="${#numbers.formatDecimal(plan.priceUsd, 1, 2)}">0.00</span>
                            </small>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" 
                                    th:onclick="'editPlan(' + ${plan.id} + ')'">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-outline-warning btn-sm"
                                    th:onclick="'toggleStatus(' + ${plan.id} + ')'">
                                <i class="fas fa-power-off"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm"
                                    th:onclick="'deletePlan(' + ${plan.id} + ')'">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Plan Modal -->
    <div class="modal fade" id="createPlanModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Plan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form th:action="@{/admin/plans}" method="post" th:object="${newPlan}">
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Plan Name *</label>
                                <input type="text" class="form-control" id="name" th:field="*{name}" required>
                            </div>
                            <div class="col-md-6">
                                <label for="difficultyLevel" class="form-label">Difficulty Level *</label>
                                <select class="form-control" id="difficultyLevel" th:field="*{difficultyLevel}" required>
                                    <option value="">Select Difficulty</option>
                                    <option value="BEGINNER">Beginner</option>
                                    <option value="INTERMEDIATE">Intermediate</option>
                                    <option value="ADVANCED">Advanced</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="durationHours" class="form-label">Duration (Hours) *</label>
                                <input type="number" class="form-control" id="durationHours" th:field="*{durationHours}" min="1" max="24" required>
                            </div>
                            <div class="col-md-6">
                                <label for="maxParticipants" class="form-label">Max Participants *</label>
                                <input type="number" class="form-control" id="maxParticipants" th:field="*{maxParticipants}" min="1" max="100" required>
                            </div>
                            <div class="col-md-6">
                                <label for="priceUsd" class="form-label">Price (USD) *</label>
                                <input type="number" class="form-control" id="priceUsd" th:field="*{priceUsd}" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-6">
                                <label for="status" class="form-label">Status *</label>
                                <select class="form-control" id="status" th:field="*{status}" required>
                                    <option value="ACTIVE">Active</option>
                                    <option value="INACTIVE">Inactive</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label for="description" class="form-label">Description *</label>
                                <textarea class="form-control" id="description" th:field="*{description}" rows="4" required></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Plan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function editPlan(id) {
            // Implement edit functionality
            alert('Edit plan functionality - Plan ID: ' + id);
        }

        function toggleStatus(id) {
            if (confirm('Are you sure you want to toggle the status of this plan?')) {
                fetch(`/admin/plans/${id}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while updating the plan status.');
                });
            }
        }

        function deletePlan(id) {
            if (confirm('Are you sure you want to delete this plan? This action cannot be undone.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/plans/${id}/delete`;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
