# Test configuration for Treasure Hunt Application
spring:
  application:
    name: treasure-hunt-registration-test
  
  # Use H2 in-memory database for testing
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA/Hibernate Configuration for testing
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false
  
  # H2 Console (for debugging tests if needed)
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Disable mail for testing
  mail:
    host: localhost
    port: 25
    username: <EMAIL>
    password: testpassword
    properties:
      mail:
        smtp:
          auth: false
          starttls:
            enable: false

# Test-specific application properties
app:
  file-storage:
    upload-dir: target/test-uploads
    max-photo-size: 1048576  # 1MB for testing
    max-document-size: 2097152  # 2MB for testing
    allowed-photo-types: image/jpeg,image/jpg,image/png
    allowed-document-types: application/pdf,image/jpeg,image/jpg
  
  email:
    from: <EMAIL>
    support: <EMAIL>
    company-name: Test Company
  
  security:
    admin:
      username: testadmin
      password: testpass

# Logging configuration for tests
logging:
  level:
    com.treasurehunt: INFO
    org.springframework.security: WARN
    org.springframework.mail: WARN
    org.hibernate: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
